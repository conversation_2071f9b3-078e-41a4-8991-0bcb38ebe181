import { useState } from "react";
import {
  FaSearch,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaUsers,
} from "react-icons/fa";

export default function BookingSearchBox({ className = "", style = {} }) {
  const [searchData, setSearchData] = useState({
    location: "",
    arrival: "",
    departure: "",
    guests: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    console.log("Search data:", searchData);
  };

  return (
    <div
      className={`relative bg-white rounded-3xl shadow-xl max-w-4xl w-[95%] md:w-4/5 lg:w-3/5 mx-auto px-4 md:px-8 py-6 md:py-8 flex flex-col items-start ${className}`}
      style={{
        boxShadow:
          "0 8px 24px 0 rgba(0, 153, 255, 0.12), 0 8px 24px 0 #b6e6ff4d",
        ...style,
      }}
    >
      <h1 className="text-2xl md:text-4xl font-extrabold text-sky-600 mb-1 md:mb-2">
        Bonjour !
      </h1>
      <p className="text-sm md:text-xl text-black mb-4 md:mb-6">
        Explorez de magnifiques endroits dans le monde avec nous
      </p>
      <div className="w-full border-b border-gray-200 mb-4" />

      <form
        onSubmit={handleSearch}
        className="relative w-full flex flex-col md:flex-row items-stretch gap-4 md:gap-0"
      >
        {/* Location Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0 border-b md:border-b-0 border-gray-200">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaMapMarkerAlt className="text-lg text-sky-600" />
            <span>Lieu</span>
          </span>
          <input
            type="text"
            name="location"
            placeholder="Ajouter une destination"
            value={searchData.location}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
            autoComplete="off"
          />
        </div>

        {/* Divider */}
        <div className="hidden md:block w-px bg-gray-200 my-3" />

        {/* Arrival Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0 border-b md:border-b-0 border-gray-200">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaCalendarAlt className="text-lg text-sky-600" />
            <span>Arrivée</span>
          </span>
          <input
            type="date"
            name="arrival"
            placeholder="Add dates"
            value={searchData.arrival}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
          />
        </div>

        {/* Divider */}
        <div className="hidden md:block w-px bg-gray-200 my-3" />

        {/* Departure Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0 border-b md:border-b-0 border-gray-200">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaCalendarAlt className="text-lg text-sky-600" />
            <span>Départ</span>
          </span>
          <input
            type="date"
            name="departure"
            placeholder="Add dates"
            value={searchData.departure}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
          />
        </div>

        {/* Divider */}
        <div className="hidden md:block w-px bg-gray-200 my-3" />

        {/* Guests Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaUsers className="text-lg text-sky-600" />
            <span>Invités</span>
          </span>
          <input
            type="number"
            name="guests"
            min="1"
            placeholder="Add guests"
            value={searchData.guests}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
          />
        </div>

        {/* Search Button */}
        <div className="flex justify-end mt-4 md:mt-0">
          <button
            type="submit"
            className="bg-yellow-400 hover:bg-yellow-500 text-white p-4 rounded-full shadow-lg transition-colors duration-200 flex items-center justify-center"
            style={{
              boxShadow: "0 4px 16px 0 rgba(0, 153, 255, 0.18)",
              width: "48px",
              height: "48px",
            }}
            aria-label="Rechercher"
          >
            <FaSearch className="w-5 h-5" />
          </button>
        </div>
      </form>
    </div>
  );
}
