import Image from "next/image";

export default function HotelOfferDetail() {
  const galleryImages = [
    "/images/ExploreTunisia/exploretunisia1.jpg",
    "/images/ExploreTunisia/exploretunisia2.jpg",
    "/images/ExploreTunisia/exploretunisia1.jpg",
    "/images/ExploreTunisia/exploretunisia3.jpg",
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
      {/* Gallery */}

      {/* Header Info */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-4xl font-bold text-gray-800">Hôtel Kanta</h1>
          <p className="text-lg text-gray-500">
            Sousse, Tunisie Station touristique
          </p>
          <div className="flex items-center mt-2 space-x-1 text-yellow-500 text-xl">
            {"★★★★☆"}
          </div>
        </div>
        <div className="mt-4 md:mt-0 flex gap-3">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700">
            Partager
          </button>
          <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg shadow hover:bg-gray-300">
            Enregistrer
          </button>
          <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg shadow hover:bg-gray-300">
            Imprimer
          </button>
        </div>
      </div>

      {/* Rating badge */}
      <div className="bg-white shadow p-4 rounded-lg max-w-xs">
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-gray-700">Adorable</span>
          <span className="bg-blue-100 text-blue-800 font-bold px-3 py-1 rounded-full">
            14/20
          </span>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2 h-96 relative rounded-xl overflow-hidden shadow-lg">
          <Image
            src="/images/ExploreTunisia/explorebackground.jpg"
            alt="Hôtel Kanta"
            layout="fill"
            objectFit="cover"
            className="rounded-xl"
          />
        </div>
        <div className="grid grid-cols-2 gap-2">
          {galleryImages.map((img, index) => (
            <div
              key={index}
              className="relative h-44 rounded-lg overflow-hidden shadow"
            >
              <Image
                src={img}
                alt={`Photo ${index}`}
                layout="fill"
                objectFit="cover"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Availability Search */}
      <form className="grid grid-cols-1 md:grid-cols-4 gap-4 bg-white p-6 rounded-lg shadow">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Arrivée
          </label>
          <input
            type="date"
            className="w-full border border-gray-300 p-2 rounded-lg"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Départ
          </label>
          <input
            type="date"
            className="w-full border border-gray-300 p-2 rounded-lg"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Chambre et occupation
          </label>
          <select className="w-full border border-gray-300 p-2 rounded-lg">
            <option>1 chambre, 2 adultes</option>
          </select>
        </div>
        <div className="flex items-end">
          <button className="w-full bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 shadow">
            Vérifier la disponibilité
          </button>
        </div>
      </form>

      {/* Description */}
      <section className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">
          Description
        </h2>
        <p className="text-gray-700 leading-relaxed">
          Découvrez les merveilles de Port El-Kantaoui en choisissant
          l&apos;hôtel Kanta comme lieu de séjour. L’hôtel Kanta jouit d’un
          emplacement idéal avec des aménagements modernes dans toutes les
          chambres et un service exceptionnel.
        </p>
      </section>

      {/* Services */}
      <section className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">
          Services & Équipements
        </h2>
        <ul className="list-disc list-inside text-gray-700 space-y-1">
          <li>Deux grandes piscines avec bassins pour enfants et toboggan</li>
          <li>Piscine couverte chauffée</li>
          <li>Salle de remise en forme</li>
          <li>Centre de spa avec service de massage et sauna</li>
          <li>Wi-Fi gratuit dans tout l&apos;hôtel</li>
          <li>Parking privé gratuit</li>
        </ul>
      </section>
    </div>
  );
}
