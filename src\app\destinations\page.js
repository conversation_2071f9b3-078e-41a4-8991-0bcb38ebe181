import Header from "@/components/header/Header.jsx";
import Footer from "@/components/footer/Footer.jsx";
import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "Destinations Tunisie - Bouguerrouche Travel",
  description:
    "Découvrez nos destinations en Tunisie : Tunis, Sousse, Djerba, Hammamet et plus. Réservez votre voyage avec Bouguerrouche Travel.",
  keywords:
    "destinations Tunisie, Tunis, Sousse, Djerba, Hammamet, voyage Tunisie",
};

const destinations = [
  {
    id: 1,
    name: "Tunis",
    region: "Nord de la Tunisie",
    description:
      "La capitale historique de la Tunisie, riche en culture et en histoire. Découvrez la médina classée au patrimoine mondial de l'UNESCO.",
    image: "/images/ExploreTunisia/exploretunisia1.jpg",
    highlights: [
      "Médina de Tunis",
      "Musée du Bardo",
      "Carthage",
      "Sidi Bou Saïd",
    ],
    bestTime: "Mars - Mai, Septembre - Novembre",
    duration: "3-4 jours",
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    region: "Centre-Est",
    description:
      "Station balnéaire populaire avec des plages magnifiques et une médina historique. Parfait pour allier détente et culture.",
    image: "/images/ExploreTunisia/exploretunisia2.jpg",
    highlights: [
      "Plages de Sousse",
      "Médina de Sousse",
      "Port El Kantaoui",
      "Monastir",
    ],
    bestTime: "Mai - Octobre",
    duration: "5-7 jours",
  },
  {
    id: 3,
    name: "Djerba",
    region: "Sud-Est",
    description:
      "L'île aux mille palmiers, destination paradisiaque avec des plages de sable fin et une culture berbère authentique.",
    image: "/images/ExploreTunisia/exploretunisia3.jpg",
    highlights: [
      "Plages de Djerba",
      "Houmt Souk",
      "Synagogue de la Ghriba",
      "Villages berbères",
    ],
    bestTime: "Avril - Juin, Septembre - Novembre",
    duration: "5-8 jours",
  },
  {
    id: 4,
    name: "Hammamet",
    region: "Nord-Est",
    description:
      "Perle du golfe de Hammamet, réputée pour ses plages, ses spas et son ambiance relaxante. Destination wellness par excellence.",
    image: "/images/back.png",
    highlights: [
      "Plages d'Hammamet",
      "Médina d'Hammamet",
      "Yasmine Hammamet",
      "Centres de thalassothérapie",
    ],
    bestTime: "Mai - Octobre",
    duration: "4-6 jours",
  },
  {
    id: 5,
    name: "Tozeur",
    region: "Sud-Ouest",
    description:
      "Porte du désert du Sahara, oasis spectaculaire avec des palmeraies et des paysages désertiques à couper le souffle.",
    image: "/images/back.png",
    highlights: [
      "Oasis de Tozeur",
      "Chott el-Jérid",
      "Douz",
      "Excursions dans le Sahara",
    ],
    bestTime: "Octobre - Avril",
    duration: "3-5 jours",
  },
  {
    id: 6,
    name: "Kairouan",
    region: "Centre",
    description:
      "Ville sainte de l'Islam, quatrième ville sainte après La Mecque, Médine et Jérusalem. Patrimoine religieux exceptionnel.",
    image: "/images/back.png",
    highlights: [
      "Grande Mosquée",
      "Médina de Kairouan",
      "Bassins des Aghlabides",
      "Artisanat traditionnel",
    ],
    bestTime: "Mars - Mai, Septembre - Novembre",
    duration: "2-3 jours",
  },
];

export default function Destinations() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-center justify-center">
          <img
            src="/images/landingpagebackground.svg"
            alt="Destinations Tunisia background"
            className="absolute inset-0 w-full h-full object-cover z-0"
          />
          <div className="relative z-10 text-center text-white px-4">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight font-poppins">
              Destinations en Tunisie
            </h1>
            <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80 mx-auto" />
            <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto font-poppins">
              Découvrez la beauté et la richesse culturelle de la Tunisie avec
              nos destinations soigneusement sélectionnées
            </p>
          </div>
        </section>

        {/* Destinations Grid */}
        <section className="w-full bg-white py-12">
          <div className="max-w-6xl mx-auto px-4">
            <div className="text-center mb-10">
              <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
                NOS DESTINATIONS PHARES
              </h2>
              <p className="text-base text-gray-700 max-w-2xl mx-auto font-poppins">
                De la capitale historique aux oasis du désert, explorez la
                diversité exceptionnelle de la Tunisie
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {destinations.map((destination) => (
                <div
                  key={destination.id}
                  className="bg-white rounded-xl shadow-sm overflow-hidden flex flex-col relative hover:scale-[1.02] transition-transform border border-gray-100"
                >
                  <div className="relative w-full h-44">
                    <Image
                      src={destination.image}
                      alt={destination.name}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 33vw"
                    />
                    <div className="absolute top-3 left-3 bg-sky-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      {destination.region}
                    </div>
                  </div>

                  <div className="flex-1 flex flex-col p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-bold text-lg text-black font-poppins">
                        {destination.name}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500 mb-2 font-poppins">
                      {destination.region}
                    </div>
                    <div className="text-xs text-gray-700 mb-3 flex flex-col gap-1 font-poppins">
                      <span>{destination.description}</span>
                    </div>

                    <div className="mb-3">
                      <h4 className="font-semibold text-gray-800 mb-2 text-sm font-poppins">
                        Points d'intérêt :
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {destination.highlights
                          .slice(0, 3)
                          .map((highlight, index) => (
                            <span
                              key={index}
                              className="bg-sky-100 text-sky-600 px-2 py-1 rounded-md text-xs font-poppins"
                            >
                              {highlight}
                            </span>
                          ))}
                      </div>
                    </div>

                    <div className="flex justify-between items-center text-xs text-gray-600 mb-3 font-poppins">
                      <div>
                        <span className="font-semibold">Période :</span>
                        <br />
                        {destination.bestTime}
                      </div>
                      <div>
                        <span className="font-semibold">Durée :</span>
                        <br />
                        {destination.duration}
                      </div>
                    </div>

                    <div className="flex items-end justify-between mt-auto pt-2">
                      <Link
                        href={`/destinations/${destination.id}`}
                        className="bg-sky-600 hover:bg-sky-700 text-white font-bold px-4 py-2 rounded-lg shadow-sm transition-colors text-sm font-poppins"
                      >
                        DÉCOUVRIR
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="w-full bg-white py-12">
          <div className="max-w-4xl mx-auto text-center px-4">
            <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
              PRÊT À PARTIR À L'AVENTURE ?
            </h2>
            <p className="text-base text-gray-700 mb-8 font-poppins">
              Contactez-nous pour planifier votre voyage sur mesure en Tunisie
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-yellow-400 hover:bg-yellow-500 text-black px-8 py-3 rounded-full font-bold text-lg shadow-sm transition-colors duration-200 font-poppins"
              >
                Nous Contacter
              </Link>
              <Link
                href="/"
                className="bg-white text-black px-8 py-3 rounded-full font-bold text-lg shadow-sm hover:bg-gray-100 transition border border-gray-200 font-poppins"
              >
                Retour à l'accueil
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
