"use client";
import { FaDollarSign, FaUserShield, FaClock } from "react-icons/fa";

export default function Hero() {
  const features = [
    {
      icon: (
        <FaDollarSign className="text-sky-500 text-5xl" strokeWidth={1.5} />
      ),
      title: "Prix Compétitifs",
      description:
        "Profitez des meilleurs tarifs du marché pour tous vos voyages, sans frais cachés.",
    },
    {
      icon: (
        <FaUserShield className="text-sky-500 text-5xl" strokeWidth={1.5} />
      ),
      title: "Réservation Sécurisée",
      description:
        "Vos paiements et données sont protégés grâce à notre système de sécurité avancé.",
    },
    {
      icon: <FaClock className="text-sky-500 text-5xl" strokeWidth={1.5} />,
      title: "Expérience Fluide",
      description:
        "Réservez en quelques clics avec une interface simple, rapide et intuitive.",
    },
  ];

  return (
    <section className="w-full bg-white py-12">
      <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
        POURQUOI NOUS CHOISIR ?
      </h2>
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12 justify-items-center">
          {features.map((feature, index) => (
            <div key={index} className="flex flex-col items-center text-center">
              <div
                className="bg-sky-100 rounded-xl shadow-sm flex items-center justify-center mb-8"
                style={{ width: 100, height: 100 }}
              >
                {feature.icon}
              </div>
              <h3 className="text-lg font-bold text-black mb-3 font-poppins">
                {feature.title}
              </h3>
              <p className="text-base text-gray-700 max-w-xs">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
