"use client";
import Header from "@/components/header/Header.jsx";
import Footer from "@/components/footer/Footer.jsx";
import { useState } from "react";
import {
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaClock,
  FaWhatsapp,
  FaFacebookF,
  FaInstagram,
} from "react-icons/fa";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
    inquiryType: "information",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      console.log("Form submitted:", formData);
      setSubmitStatus("success");
      setIsSubmitting(false);
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
        inquiryType: "information",
      });
    }, 2000);
  };

  const contactInfo = [
    {
      icon: <FaPhone className="text-2xl text-sky-600" />,
      title: "Téléphone",
      details: ["+213 123 456 789", "+213 987 654 321"],
      action: "tel:+213123456789",
    },
    {
      icon: <FaEnvelope className="text-2xl text-sky-600" />,
      title: "Email",
      details: ["<EMAIL>", "<EMAIL>"],
      action: "mailto:<EMAIL>",
    },
    {
      icon: <FaMapMarkerAlt className="text-2xl text-sky-600" />,
      title: "Adresse",
      details: [
        "Bouguerrouche Travel Service",
        "Djasr Kasentina, Alger",
        "Algérie",
      ],
      action: null,
    },
    {
      icon: <FaClock className="text-2xl text-sky-600" />,
      title: "Horaires",
      details: ["Lun - Ven: 08h00 - 18h00", "Sam: 09h00 - 16h00", "Dim: Fermé"],
      action: null,
    },
  ];

  const inquiryTypes = [
    { value: "information", label: "Demande d'information" },
    { value: "booking", label: "Réservation" },
    { value: "modification", label: "Modification de réservation" },
    { value: "complaint", label: "Réclamation" },
    { value: "partnership", label: "Partenariat" },
    { value: "other", label: "Autre" },
  ];

  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-center justify-center">
          <Image
            src="/images/landingpagebackground.svg"
            alt="Contact background"
            fill
            className="object-cover z-0"
            priority
            sizes="100vw"
          />
          <div className="relative z-10 text-center text-white px-4">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight font-poppins">
              Contactez-Nous
            </h1>
            <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80 mx-auto" />
            <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto font-poppins">
              Notre équipe est à votre disposition pour répondre à toutes vos
              questions
            </p>
          </div>
        </section>

        {/* Contact Information Cards */}
        <section className="py-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {contactInfo.map((info, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-sm p-6 text-center hover:shadow-md transition-shadow duration-300"
                >
                  <div className="flex justify-center mb-4">{info.icon}</div>
                  <h3 className="text-xl font-bold text-black mb-3 font-poppins">
                    {info.title}
                  </h3>
                  <div className="space-y-1">
                    {info.details.map((detail, idx) => (
                      <p
                        key={idx}
                        className="text-gray-600 text-sm font-poppins"
                      >
                        {info.action && idx === 0 ? (
                          <a
                            href={info.action}
                            className="hover:text-sky-600 transition-colors duration-200"
                          >
                            {detail}
                          </a>
                        ) : (
                          detail
                        )}
                      </p>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Contact Form and Map */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
                  Envoyez-nous un Message
                </h2>

                {submitStatus === "success" && (
                  <div className="bg-sky-50 border border-sky-200 rounded-lg p-4 mb-6">
                    <p className="text-sky-800 font-medium font-poppins">
                      ✅ Votre message a été envoyé avec succès ! Nous vous
                      répondrons dans les plus brefs délais.
                    </p>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="name"
                        className="block text-sm font-medium text-gray-700 mb-2 font-poppins"
                      >
                        Nom complet *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors duration-200 font-poppins"
                        placeholder="Votre nom complet"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium text-gray-700 mb-2 font-poppins"
                      >
                        Email *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors duration-200 font-poppins"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="phone"
                        className="block text-sm font-medium text-gray-700 mb-2 font-poppins"
                      >
                        Téléphone
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors duration-200 font-poppins"
                        placeholder="+213 XXX XXX XXX"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="inquiryType"
                        className="block text-sm font-medium text-gray-700 mb-2 font-poppins"
                      >
                        Type de demande *
                      </label>
                      <select
                        id="inquiryType"
                        name="inquiryType"
                        value={formData.inquiryType}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors duration-200 font-poppins"
                      >
                        {inquiryTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="subject"
                      className="block text-sm font-medium text-gray-700 mb-2 font-poppins"
                    >
                      Sujet *
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors duration-200 font-poppins"
                      placeholder="Sujet de votre message"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-medium text-gray-700 mb-2 font-poppins"
                    >
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows="6"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors duration-200 font-poppins"
                      placeholder="Votre message..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-sky-600 hover:bg-sky-700 text-white py-3 rounded-lg font-semibold transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-poppins"
                  >
                    {isSubmitting ? "Envoi en cours..." : "Envoyer le Message"}
                  </button>
                </form>
              </div>

              {/* Map */}
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
                  Notre Localisation
                </h2>
                <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3196.1234567890123!2d3.123456789012345!3d36.12345678901234!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzbCsDA3JzI0LjUiTiAzwrAwNyc0NS4xIkU!5e0!3m2!1sfr!2sdz!4v1234567890123"
                    width="100%"
                    height="450"
                    style={{ border: 0 }}
                    allowFullScreen=""
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Social Media Links */}
        <section className="py-16 bg-sky-600">
          <div className="max-w-4xl mx-auto text-center px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 font-poppins">
              Suivez-Nous
            </h2>
            <p className="text-xl text-sky-100 mb-8 font-poppins">
              Restez connecté avec nous sur les réseaux sociaux
            </p>
            <div className="flex justify-center gap-6">
              <a
                href="https://facebook.com"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-sky-600 p-4 rounded-full hover:bg-sky-50 transition-colors duration-200"
              >
                <FaFacebookF className="text-2xl" />
              </a>
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-sky-600 p-4 rounded-full hover:bg-sky-50 transition-colors duration-200"
              >
                <FaInstagram className="text-2xl" />
              </a>
              <a
                href="https://wa.me/213123456789"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-sky-600 p-4 rounded-full hover:bg-sky-50 transition-colors duration-200"
              >
                <FaWhatsapp className="text-2xl" />
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
