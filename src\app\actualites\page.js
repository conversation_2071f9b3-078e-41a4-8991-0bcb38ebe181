import Header from "@/components/header/Header.jsx";
import Footer from "@/components/footer/Footer.jsx";
import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "Actualités Voyage - Promotions et Nouveautés | Bouguerrouche Travel",
  description:
    "Découvrez nos dernières promotions, nouveautés et actualités voyage. Offres spéciales pour la Tunisie avec Bouguerrouche Travel.",
  keywords:
    "actualités voyage, promotions Tunisie, offres spéciales, nouveautés voyage",
};

const news = [
  {
    id: 1,
    title: "Offre Spéciale Ramadan 2024 - Jusqu'à 30% de Réduction",
    excerpt:
      "Profitez de nos tarifs exceptionnels pour vos voyages en Tunisie pendant le mois sacré. Réservations ouvertes dès maintenant.",
    image: "/images/ExploreTunisia/exploretunisia1.jpg",
    type: "Promotion",
    date: "20 Décembre 2024",
    urgent: true,
    validUntil: "31 Janvier 2025",
  },
  {
    id: 2,
    title: "Nouvelle Destination : Découvrez Tozeur et le Sahara",
    excerpt:
      "Nous sommes fiers d'ajouter Tozeur à notre catalogue. Explorez les oasis et le désert avec nos circuits exclusifs.",
    image: "/images/ExploreTunisia/exploretunisia2.jpg",
    type: "Nouveauté",
    date: "18 Décembre 2024",
    urgent: false,
  },
  {
    id: 3,
    title: "Partenariat avec Radisson Blu Hammamet",
    excerpt:
      "Nouveau partenariat exclusif avec Radisson Blu pour des séjours de luxe à Hammamet. Tarifs préférentiels pour nos clients.",
    image: "/images/ExploreTunisia/exploretunisia3.jpg",
    type: "Partenariat",
    date: "15 Décembre 2024",
    urgent: false,
  },
  {
    id: 4,
    title: "Conditions de Voyage Mises à Jour",
    excerpt:
      "Nouvelles conditions d'entrée en Tunisie et protocoles sanitaires. Consultez les dernières informations officielles.",
    image: "/images/back.png",
    type: "Information",
    date: "12 Décembre 2024",
    urgent: true,
  },
  {
    id: 5,
    title: "Early Bird 2025 - Réservez Maintenant et Économisez",
    excerpt:
      "Réservez vos vacances d'été 2025 dès maintenant et bénéficiez de 20% de réduction sur tous nos packages.",
    image: "/images/back.png",
    type: "Promotion",
    date: "10 Décembre 2024",
    urgent: false,
    validUntil: "28 Février 2025",
  },
  {
    id: 6,
    title: "Nouveau Bureau à Constantine",
    excerpt:
      "Bouguerrouche Travel ouvre son nouveau bureau à Constantine pour mieux vous servir dans l'Est algérien.",
    image: "/images/back.png",
    type: "Nouveauté",
    date: "8 Décembre 2024",
    urgent: false,
  },
];

const newsTypes = [
  { name: "Toutes", color: "gray" },
  { name: "Promotion", color: "sky" },
  { name: "Nouveauté", color: "sky" },
  { name: "Partenariat", color: "sky" },
  { name: "Information", color: "yellow" },
];

const getTypeColor = (type) => {
  const typeConfig = newsTypes.find((t) => t.name === type);
  const colors = {
    sky: "bg-sky-100 text-sky-600",
    yellow: "bg-yellow-100 text-yellow-600",
    gray: "bg-gray-100 text-gray-700",
  };
  return colors[typeConfig?.color] || colors.gray;
};

export default function Actualites() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-center justify-center">
          <Image
            src="/images/landingpagebackground.svg"
            alt="Actualités background"
            fill
            className="object-cover z-0"
            priority
            sizes="100vw"
          />
          <div className="relative z-10 text-center text-white px-4">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight font-poppins">
              Actualités
            </h1>
            <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80 mx-auto" />
            <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto font-poppins">
              Restez informé de nos dernières promotions, nouveautés et
              informations voyage
            </p>
          </div>
        </section>

        {/* Filter Tabs */}
        <section className="py-8 bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex flex-wrap gap-3 justify-center">
              {newsTypes.map((type, index) => (
                <button
                  key={index}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 font-poppins ${
                    index === 0
                      ? "bg-sky-600 text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-600"
                  }`}
                >
                  {type.name}
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Urgent News Banner */}
        <section className="py-6 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="bg-sky-50 border-l-4 border-sky-500 p-4 rounded-lg">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-sky-500"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-sky-800 font-poppins">
                    Informations Importantes
                  </h3>
                  <div className="mt-2 text-sm text-sky-700 font-poppins">
                    <p>
                      Consultez nos dernières mises à jour concernant les
                      conditions de voyage et les promotions en cours.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* News Grid */}
        <section className="py-12 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {news.map((item) => (
                <article
                  key={item.id}
                  className={`bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300 ${
                    item.urgent ? "ring-2 ring-sky-200" : ""
                  }`}
                >
                  {item.urgent && (
                    <div className="bg-sky-600 text-white text-center py-2 text-sm font-semibold font-poppins">
                      🚨 URGENT
                    </div>
                  )}

                  <div className="relative h-48">
                    <Image
                      src={item.image}
                      alt={item.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>

                  <div className="p-6">
                    <div className="flex items-center gap-3 mb-3">
                      <span
                        className={`px-2 py-1 rounded-md text-xs font-medium font-poppins ${getTypeColor(
                          item.type
                        )}`}
                      >
                        {item.type}
                      </span>
                      <span className="text-gray-500 text-xs font-poppins">
                        {item.date}
                      </span>
                    </div>

                    <h3 className="text-xl font-bold text-black mb-3 line-clamp-2 font-poppins">
                      {item.title}
                    </h3>

                    <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3 font-poppins">
                      {item.excerpt}
                    </p>

                    {item.validUntil && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                        <p className="text-yellow-800 text-sm font-medium font-poppins">
                          ⏰ Valable jusqu&apos;au {item.validUntil}
                        </p>
                      </div>
                    )}

                    <Link
                      href={`/actualites/${item.id}`}
                      className="block w-full bg-sky-600 hover:bg-sky-700 text-white text-center py-3 rounded-lg font-semibold transition-colors duration-200 font-poppins"
                    >
                      Lire Plus
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Subscription */}
        <section className="bg-sky-600 py-16">
          <div className="max-w-4xl mx-auto text-center px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 font-poppins">
              Ne Manquez Aucune Actualité
            </h2>
            <p className="text-xl text-sky-100 mb-8 font-poppins">
              Inscrivez-vous à notre newsletter pour recevoir nos promotions et
              nouveautés en avant-première
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <input
                type="email"
                placeholder="Votre adresse email"
                className="px-6 py-3 rounded-full text-gray-800 focus:outline-none focus:ring-2 focus:ring-sky-400 font-poppins"
              />
              <button className="bg-yellow-400 hover:bg-yellow-500 text-black px-8 py-3 rounded-full font-bold text-lg shadow-sm transition-colors duration-200 font-poppins">
                S&apos;inscrire
              </button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
