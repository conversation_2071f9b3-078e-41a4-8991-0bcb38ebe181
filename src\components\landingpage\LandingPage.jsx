"use client";
import BookingSearchBox from "./BookingSearchBox";
import WhatsAppBubble from "../WhatsAppBubble/WhatsAppBubble";
import TopBookNow from "./TopBookNow";
import Hero from "../hero/Hero";
import Partners from "../hero/Partners";

export default function LandingPage() {
  return (
    <>
      <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-end justify-center">
        <img
          src="/images/landingpagebackground.svg"
          alt="Travel background"
          className="absolute inset-0 w-full h-full object-cover z-0"
        />
      </section>
      <div className="relative z-30 -mt-16 flex justify-center">
        <BookingSearchBox />
      </div>
      <div className="h-24 md:h-28 lg:h-32" />
      <Hero />
      <Partners />
      <TopBookNow />

      <WhatsAppBubble />
    </>
  );
}
