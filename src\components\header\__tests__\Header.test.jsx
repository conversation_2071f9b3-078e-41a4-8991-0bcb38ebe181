import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import Header from '../Header';
import { axe, toHaveNoViolations } from 'jest-axe';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

// Mock window.scrollY
Object.defineProperty(window, 'scrollY', {
  writable: true,
  value: 0,
});

expect.extend(toHaveNoViolations);

describe('Header Component', () => {
  beforeEach(() => {
    usePathname.mockReturnValue('/');
    window.scrollY = 0;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the header with logo and navigation', () => {
    render(<Header />);
    
    // Check logo
    expect(screen.getByAltText('Bouguerrouche Travel')).toBeInTheDocument();
    
    // Check navigation items
    expect(screen.getByText('Accueil')).toBeInTheDocument();
    expect(screen.getByText('Destinations')).toBeInTheDocument();
    expect(screen.getByText('Blog')).toBeInTheDocument();
    expect(screen.getByText('Actualités')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('shows contact bar on desktop', () => {
    render(<Header />);
    
    expect(screen.getByText('+213 123 456 789')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Votre partenaire de confiance pour des voyages inoubliables')).toBeInTheDocument();
  });

  it('highlights active navigation link', () => {
    usePathname.mockReturnValue('/destinations');
    render(<Header />);
    
    const destinationsLink = screen.getAllByText('Destinations')[0];
    expect(destinationsLink).toHaveClass('text-[#006C95]');
  });

  it('toggles mobile menu when button is clicked', async () => {
    render(<Header />);
    
    const menuButton = screen.getByLabelText('Ouvrir le menu');
    fireEvent.click(menuButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Fermer le menu')).toBeInTheDocument();
    });
    
    // Check if mobile menu items are visible
    const mobileMenuItems = screen.getAllByText('Accueil');
    expect(mobileMenuItems.length).toBeGreaterThan(1); // Desktop + Mobile
  });

  it('closes mobile menu when navigation link is clicked', async () => {
    render(<Header />);
    
    // Open mobile menu
    const menuButton = screen.getByLabelText('Ouvrir le menu');
    fireEvent.click(menuButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Fermer le menu')).toBeInTheDocument();
    });
    
    // Click a navigation link in mobile menu
    const mobileLinks = screen.getAllByText('Destinations');
    const mobileDestinationsLink = mobileLinks.find(link => 
      link.closest('#mobile-menu')
    );
    
    if (mobileDestinationsLink) {
      fireEvent.click(mobileDestinationsLink);
    }
    
    await waitFor(() => {
      expect(screen.getByLabelText('Ouvrir le menu')).toBeInTheDocument();
    });
  });

  it('changes background on scroll', () => {
    render(<Header />);
    
    // Initially transparent
    const header = screen.getByRole('banner');
    expect(header).toHaveClass('bg-transparent');
    
    // Simulate scroll
    window.scrollY = 100;
    fireEvent.scroll(window);
    
    // Should have background after scroll
    waitFor(() => {
      expect(header).toHaveClass('bg-white/95');
    });
  });

  it('has proper accessibility attributes', () => {
    render(<Header />);
    
    // Check ARIA labels
    expect(screen.getByRole('banner')).toBeInTheDocument();
    expect(screen.getByLabelText('Navigation principale')).toBeInTheDocument();
    expect(screen.getByLabelText('Bouguerrouche Travel - Accueil')).toBeInTheDocument();
    
    // Check mobile menu accessibility
    const menuButton = screen.getByLabelText('Ouvrir le menu');
    expect(menuButton).toHaveAttribute('aria-expanded', 'false');
    expect(menuButton).toHaveAttribute('aria-controls', 'mobile-menu');
  });

  it('should not have accessibility violations', async () => {
    const { container } = render(<Header />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('renders CTA button with correct styling', () => {
    render(<Header />);
    
    const ctaButtons = screen.getAllByText('Réserver maintenant');
    expect(ctaButtons.length).toBeGreaterThan(0);
    
    // Check desktop CTA button
    const desktopCTA = ctaButtons[0];
    expect(desktopCTA).toHaveClass('bg-yellow-400');
    expect(desktopCTA).toHaveClass('font-poppins');
  });

  it('uses brand colors consistently', () => {
    usePathname.mockReturnValue('/');
    render(<Header />);
    
    // Check brand color usage
    const activeLink = screen.getAllByText('Accueil')[0];
    expect(activeLink).toHaveClass('text-[#006C95]');
    
    // Check contact bar background
    const contactBar = screen.getByText('+213 123 456 789').closest('div').closest('div');
    expect(contactBar).toHaveClass('bg-[#006C95]');
  });
});
