'useclient';
import Image from "next/image";
export default function Email() {
  return (
    <div className="flex justify-center items-center py-12 bg-white">
      <div className="flex flex-col md:flex-row items-center bg-gray-100 rounded-3xl overflow-hidden max-w-6xl w-full shadow-md">
        {/* Left Image */}
        <div className="w-full md:w-1/2 relative aspect-video md:aspect-auto md:h-96">
          <Image
            src="/images/email.jpg"
            alt="Luxury Resort"
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Right Content */}
        <div className="w-full md:w-1/2 p-8 text-center md:text-left">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">
            Bénéficiez d'offres spéciales et plus, <br />
            encore de  <span className="text-black">BOUGUERROUCHE</span>
          </h2>

          {/* Email input with button inside */}
          <form className="relative max-w-xl mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full rounded-full px-6 py-4 pr-36 text-gray-700 shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-400 text-white font-semibold rounded-full px-6 py-2 shadow-md hover:bg-blue-600 transition"
            >
              Subscribe
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
