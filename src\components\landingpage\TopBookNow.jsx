import Image from "next/image";

const hotels = [
  {
    name: "Joya Paradise",
    location: "Djerba - Tunisie",
    stars: 4,
    offer: "Offre exclusive - bon emplacement - 1er enf -6 ans gratuit - luxe",
    price: 13500,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: [],
  },
  {
    name: "Hôtel Kanta",
    location: "Sousse - Tunisie",
    stars: 4,
    offer: "1er Enfant -6 ans Gratuit",
    price: 11700,
    currency: "DA",
    image: "/images/back.png",
    discount: "-18%",
    highlights: [],
  },
  {
    name: "Travelodo Village Africa Jade Thalasso",
    location: "Korba - Tunisie",
    stars: 4,
    offer:
      "1er enfant -12ans gratuit / réduction -50% sur 2 nuit avec samedi // réduction -50% senior +60ans sauf les samedis / Early Booking 20% pour toute réservation effectuée avant le 31/05 pour les séjours",
    price: 28000,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: [],
  },
  {
    name: "Radisson Blu Resort & Thalasso Hammamet",
    location: "Hammamet - Tunisie",
    stars: 5,
    offer: "ACTION EARLY BOOKING REDUCTION 15%",
    price: 19500,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: ["green"],
  },
  {
    name: "Yadis Hammamet",
    location: "Hammamet - Tunisie",
    stars: 4,
    offer:
      "Hôtel en Promo exclusive - 1er enfant -6ans Gratuit sauf la période du 04/07 au 13/09 - N'accepte pas les jeunes célibataires",
    price: 12600,
    currency: "DA",
    image: "/images/back.png",
    discount: "-29%",
    highlights: ["green"],
  },
  {
    name: "Riviera",
    location: "Sousse - Tunisie",
    stars: 4,
    offer:
      "N'accepte pas les jeunes célibataires / Promo Enfant -6 ans gratuit",
    price: 16800,
    currency: "DA",
    image: "/images/back.png",
    discount: null,
    highlights: ["green"],
  },
];

export default function TopBookNow() {
  return (
    <section className="w-full py-12 bg-white">
      <h2 className="text-3xl md:text-4xl font-extrabold text-center mb-10 tracking-tight text-[#006C95]">
        TOP BOOK NOW
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto px-4">
        {hotels.map((hotel, idx) => (
          <div
            key={idx}
            className="bg-[#f7fafc] rounded-2xl shadow-lg overflow-hidden flex flex-col relative hover:scale-[1.02] transition-transform border border-[#e0e7ef]"
          >
            <div className="relative w-full h-44">
              <Image
                src={hotel.image}
                alt={hotel.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
              {hotel.discount && (
                <span className="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow">
                  {hotel.discount}
                </span>
              )}
            </div>
            <div className="flex-1 flex flex-col p-4">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-bold text-lg text-[#222]">
                  {hotel.name}
                </span>
                <span className="text-yellow-400 text-sm">
                  {"★".repeat(hotel.stars)}
                </span>
              </div>
              <div className="text-sm text-gray-500 mb-2">{hotel.location}</div>
              <div className="text-xs text-gray-700 mb-3 flex flex-col gap-1">
                {hotel.offer && (
                  <span
                    className={
                      hotel.highlights.includes("green")
                        ? "text-green-600 font-semibold"
                        : ""
                    }
                  >
                    {hotel.offer}
                  </span>
                )}
              </div>
              <div className="flex items-end justify-between mt-auto pt-2">
                <div className="text-base font-bold text-[#006C95]">
                  à partir de{" "}
                  <span className="text-xl text-black">
                    {hotel.price.toLocaleString()} {hotel.currency}
                  </span>
                </div>
                <button className="bg-[#006C95] hover:bg-[#005377] text-white font-bold px-4 py-2 rounded-lg shadow transition-colors text-sm">
                  VOIR L'OFFRE
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
