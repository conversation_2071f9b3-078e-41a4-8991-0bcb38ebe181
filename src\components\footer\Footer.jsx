"use client";
import Link from "next/link";
import {
  FaFacebookF,
  FaTwitter,
  FaTiktok,
  FaYoutube,
  FaInstagram,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
} from "react-icons/fa";

export default function Footer() {
  const footerLinks = {
    support: [
      { label: "Centre d'aide", href: "/help" },
      { label: "Informations de sécurité", href: "/safety" },
      { label: "Options d'annulation", href: "/cancellation" },
      { label: "Assistance 24/7", href: "/support" },
    ],
    company: [
      { label: "À propos", href: "/about" },
      { label: "Politique de confidentialité", href: "/privacy" },
      { label: "Blog communautaire", href: "/blog" },
      { label: "Conditions d'utilisation", href: "/terms" },
    ],
    contact: [
      { label: "FAQ", href: "/faq" },
      { label: "Nous contacter", href: "/contact" },
      { label: "Partenariats", href: "/partnerships" },
      { label: "Carrières", href: "/careers" },
    ],
  };

  const socialLinks = [
    { icon: <FaFacebookF />, href: "https://facebook.com", label: "Facebook" },
    {
      icon: <FaInstagram />,
      href: "https://instagram.com",
      label: "Instagram",
    },
    { icon: <FaTiktok />, href: "https://tiktok.com", label: "TikTok" },
    { icon: <FaYoutube />, href: "https://youtube.com", label: "YouTube" },
    { icon: <FaTwitter />, href: "https://twitter.com", label: "Twitter" },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Company Info */}
          <div className="space-y-4">
            <img
              src="/images/logo.svg"
              alt="Bouguerrouche Travel"
              className="h-12 w-auto"
            />
            <p className="text-gray-400 text-sm">
              Votre partenaire de confiance pour des voyages inoubliables à
              travers le monde.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-3 text-gray-400">
                <FaPhone className="text-sky-500" />
                <span>+*********** 789</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <FaEnvelope className="text-sky-500" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <FaMapMarkerAlt className="text-sky-500" />
                <span>Alger, Algérie</span>
              </div>
            </div>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="font-bold text-lg mb-4">Support</h4>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-sky-500 transition-colors duration-200"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="font-bold text-lg mb-4">Entreprise</h4>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-sky-500 transition-colors duration-200"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Links */}
          <div>
            <h4 className="font-bold text-lg mb-4">Contact</h4>
            <ul className="space-y-3">
              {footerLinks.contact.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-sky-500 transition-colors duration-200"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Links */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex space-x-6 mb-4 md:mb-0">
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-sky-500 transition-colors duration-200"
                  aria-label={social.label}
                >
                  {social.icon}
                </a>
              ))}
            </div>
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} Bouguerrouche Travel. Tous droits
              réservés.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
