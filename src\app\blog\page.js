import Header from "@/components/header/Header.jsx";
import Footer from "@/components/footer/Footer.jsx";
import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "Blog Voyage - Guides et Conseils Tunisie | Bouguerrouche Travel",
  description:
    "Découvrez nos guides de voyage, conseils pratiques et articles sur la Tunisie. Préparez votre séjour avec nos experts.",
  keywords:
    "blog voyage Tunisie, guide voyage, conseils voyage, culture tunisienne",
};

const blogPosts = [
  {
    id: 1,
    title: "10 Incontournables à Visiter en Tunisie",
    excerpt:
      "Découvrez les sites emblématiques de la Tunisie, des ruines de Carthage aux oasis du Sahara. Notre guide complet pour ne rien manquer.",
    image: "/images/ExploreTunisia/exploretunisia1.jpg",
    category: "Guide de Voyage",
    readTime: "8 min",
    date: "15 Décembre 2024",
    author: "<PERSON><PERSON><PERSON>",
  },
  {
    id: 2,
    title: "Gastronomie Tunisienne : Saveurs à Ne Pas Manquer",
    excerpt:
      "Plongez dans l'univers culinaire tunisien. Du couscous aux pâtisseries orientales, découvrez les spécialités locales.",
    image: "/images/ExploreTunisia/exploretunisia2.jpg",
    category: "Culture & Gastronomie",
    readTime: "6 min",
    date: "10 Décembre 2024",
    author: "Chef Amina",
  },
  {
    id: 3,
    title: "Préparer son Voyage en Tunisie : Guide Pratique",
    excerpt:
      "Tout ce qu'il faut savoir avant de partir : documents, climat, monnaie, coutumes. Votre checklist complète.",
    image: "/images/ExploreTunisia/exploretunisia3.jpg",
    category: "Conseils Pratiques",
    readTime: "10 min",
    date: "5 Décembre 2024",
    author: "Mohamed Bouguerrouche",
  },
  {
    id: 4,
    title: "Les Plus Belles Plages de Tunisie",
    excerpt:
      "Sable fin, eaux cristallines et paysages paradisiaques. Notre sélection des plages incontournables.",
    image: "/images/back.png",
    category: "Destinations",
    readTime: "7 min",
    date: "1 Décembre 2024",
    author: "Équipe Bouguerrouche",
  },
  {
    id: 5,
    title: "Artisanat Tunisien : Souvenirs Authentiques",
    excerpt:
      "Tapis, poteries, bijoux... Découvrez l'artisanat traditionnel tunisien et où trouver les meilleures pièces.",
    image: "/images/back.png",
    category: "Culture & Shopping",
    readTime: "5 min",
    date: "28 Novembre 2024",
    author: "Fatima Zahra",
  },
  {
    id: 6,
    title: "Voyage en Famille en Tunisie : Nos Conseils",
    excerpt:
      "Activités, hébergements et conseils pour un séjour réussi avec des enfants en Tunisie.",
    image: "/images/back.png",
    category: "Voyage en Famille",
    readTime: "9 min",
    date: "25 Novembre 2024",
    author: "Sarah Benaissa",
  },
];

const categories = [
  "Tous les Articles",
  "Guide de Voyage",
  "Destinations",
  "Culture & Gastronomie",
  "Conseils Pratiques",
  "Voyage en Famille",
  "Culture & Shopping",
];

export default function Blog() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-center justify-center">
          <img
            src="/images/landingpagebackground.svg"
            alt="Blog Voyage background"
            className="absolute inset-0 w-full h-full object-cover z-0"
          />
          <div className="relative z-10 text-center text-white px-4">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight font-poppins">
              Blog Voyage
            </h1>
            <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80 mx-auto" />
            <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto font-poppins">
              Guides, conseils et inspirations pour votre voyage en Tunisie
            </p>
          </div>
        </section>

        {/* Categories Filter */}
        <section className="py-8 bg-white shadow-sm">
          <div className="max-w-6xl mx-auto px-4">
            <div className="flex flex-wrap gap-3 justify-center">
              {categories.map((category, index) => (
                <button
                  key={index}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 font-poppins ${
                    index === 0
                      ? "bg-sky-600 text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-sky-100 hover:text-sky-600"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Article */}
        <section className="py-12 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
              Article à la Une
            </h2>
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/2 relative h-64 md:h-auto">
                  <Image
                    src={blogPosts[0].image}
                    alt={blogPosts[0].title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </div>
                <div className="md:w-1/2 p-8">
                  <div className="flex items-center gap-4 mb-4">
                    <span className="bg-sky-100 text-sky-600 px-3 py-1 rounded-full text-sm font-medium font-poppins">
                      {blogPosts[0].category}
                    </span>
                    <span className="text-gray-500 text-sm font-poppins">
                      {blogPosts[0].readTime} de lecture
                    </span>
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold text-black mb-4 font-poppins">
                    {blogPosts[0].title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed font-poppins">
                    {blogPosts[0].excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500 font-poppins">
                      <p>Par {blogPosts[0].author}</p>
                      <p>{blogPosts[0].date}</p>
                    </div>
                    <Link
                      href={`/blog/${blogPosts[0].id}`}
                      className="bg-sky-600 hover:bg-sky-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 font-poppins"
                    >
                      Lire l'Article
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Articles Grid */}
        <section className="py-12 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
              Derniers Articles
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.slice(1).map((post) => (
                <article
                  key={post.id}
                  className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300"
                >
                  <div className="relative h-48">
                    <Image
                      src={post.image}
                      alt={post.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="bg-sky-100 text-sky-600 px-2 py-1 rounded-md text-xs font-medium font-poppins">
                        {post.category}
                      </span>
                      <span className="text-gray-500 text-xs font-poppins">
                        {post.readTime}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold text-black mb-3 line-clamp-2 font-poppins">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3 font-poppins">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-gray-500 font-poppins">
                        <p>{post.author}</p>
                        <p>{post.date}</p>
                      </div>
                      <Link
                        href={`/blog/${post.id}`}
                        className="text-sky-600 hover:text-sky-700 font-semibold text-sm transition-colors duration-200 font-poppins"
                      >
                        Lire Plus →
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Subscription */}
        <section className="bg-sky-600 py-16">
          <div className="max-w-4xl mx-auto text-center px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 font-poppins">
              Restez Informé
            </h2>
            <p className="text-xl text-sky-100 mb-8 font-poppins">
              Inscrivez-vous à notre newsletter pour recevoir nos derniers
              articles et offres spéciales
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <input
                type="email"
                placeholder="Votre adresse email"
                className="px-6 py-3 rounded-full text-gray-800 focus:outline-none focus:ring-2 focus:ring-sky-400 font-poppins"
              />
              <button className="bg-yellow-400 hover:bg-yellow-500 text-black px-8 py-3 rounded-full font-bold text-lg shadow-sm transition-colors duration-200 font-poppins">
                S'inscrire
              </button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
